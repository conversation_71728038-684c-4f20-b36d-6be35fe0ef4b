/* eslint-disable */
import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ConfigModule } from './config/config.module';
import { DatabaseModule } from './database/database.module';
import { SalesforceModule } from './salesforce/salesforce.module';

@Module({
  imports: [ConfigModule, DatabaseModule, SalesforceModule],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
