/* eslint-disable */
import { registerAs } from '@nestjs/config';

export default registerAs('salesforce', () => ({
  baseUrl: process.env.SF_BASE_URL ?? 'https://test.salesforce.com',
  authUrl: process.env.SF_AUTH_URL ?? process.env.SF_BASE_URL ?? 'https://test.salesforce.com',
  clientId: process.env.SF_CLIENT_ID ?? '',
  clientSecret: process.env.SF_CLIENT_SECRET ?? '',
  username: process.env.SF_USERNAME ?? '',
  password: process.env.SF_PASSWORD ?? '',
  token: process.env.SF_TOKEN ?? '',
  apiVersion: process.env.SF_API_VERSION ?? 'v57.0',
  tokenTtlMs: Number(process.env.SF_TOKEN_TTL_MS ?? 30 * 60 * 1000),
}));
