/* eslint-disable */
import { HttpService } from '@nestjs/axios';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AxiosError, AxiosRequestConfig } from 'axios';
import { firstValueFrom } from 'rxjs';

interface SalesforceAuthResponse {
  access_token: string;
  instance_url: string;
  id: string;
  token_type: string;
  issued_at: string;
  signature: string;
}

@Injectable()
export class SalesforceService {
  private readonly logger = new Logger(SalesforceService.name);
  private accessToken: string | null = null;
  private accessTokenExpiresAt = 0;
  private instanceUrl: string | null = null;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {}

  async request<T = unknown>(config: AxiosRequestConfig): Promise<T> {
    const token = await this.ensureAccessToken();
    const requestConfig = this.buildAuthorizedConfig(config, token);

    try {
      const response = await firstValueFrom(this.httpService.request<T>(requestConfig));
      return response.data;
    } catch (error) {
      this.handleAxiosError(error);
    }
  }

  private async ensureAccessToken(): Promise<string> {
    if (this.accessToken && Date.now() < this.accessTokenExpiresAt) {
      return this.accessToken;
    }

    try {
      const params = new URLSearchParams({
        grant_type: 'password',
        client_id: this.configService.get<string>('salesforce.clientId') ?? '',
        client_secret: this.configService.get<string>('salesforce.clientSecret') ?? '',
        username: this.configService.get<string>('salesforce.username') ?? '',
        password: `${this.configService.get<string>('salesforce.password') ?? ''}${
          this.configService.get<string>('salesforce.token') ?? ''
        }`,
      });

      const response = await this.httpService.axiosRef.post<SalesforceAuthResponse>(
        '/services/oauth2/token',
        params.toString(),
        {
          headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        },
      );

      this.accessToken = response.data.access_token;
      this.instanceUrl = response.data.instance_url ?? this.configService.get<string>('salesforce.baseUrl');

      const ttl = this.configService.get<number>('salesforce.tokenTtlMs') ?? 30 * 60 * 1000;
      this.accessTokenExpiresAt = Date.now() + ttl;

      return this.accessToken;
    } catch (error) {
      this.handleAxiosError(error);
    }
  }

  private buildAuthorizedConfig(config: AxiosRequestConfig, accessToken: string): AxiosRequestConfig {
    const headers = {
      ...(config.headers ?? {}),
      Authorization: `Bearer ${accessToken}`,
    };

    const baseURL =
      config.baseURL ?? this.instanceUrl ?? this.configService.get<string>('salesforce.baseUrl') ?? undefined;

    return {
      ...config,
      baseURL,
      headers,
    };
  }

  private handleAxiosError(error: unknown): never {
    if (error instanceof AxiosError) {
      const { response, message } = error;
      if (response) {
        this.logger.error(
          `Salesforce request failed: ${response.status} ${response.statusText} - ${JSON.stringify(response.data)}`,
        );
        throw new Error(
          `Salesforce request failed with status ${response.status}: ${JSON.stringify(response.data ?? {})}`,
        );
      }

      this.logger.error(`Salesforce request failed: ${message}`);
      throw new Error(`Salesforce request failed: ${message}`);
    }

    throw error;
  }
}
