/* eslint-disable */
import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateJvsFounderRatingsTable1720829000060 implements MigrationInterface {
  name = 'CreateJvsFounderRatingsTable1720829000060';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS jvs_founder_ratings (
              id character varying(18) NOT NULL,
              name character varying(80),
              createddate timestamp with time zone,
              account__c character varying(18),
              vision_strategic_thinking__c character varying(255),
              adaptability_learnability__c character varying(255),
              goal_orientation__c character varying(255),
              org_building_ability_to_attract_high_q__c character varying(255),
              narrative_building_story_telling__c character varying(255),
              financial_prudence_burn_management__c character varying(255),
              pace_of_execution_bias_to_action__c character varying(255),
              ability_to_take_tough_decisions__c character varying(255),
              leading_others_outcome_from_team__c character varying(255),
              self_awareness__c character varying(255),
              integrity_transparency__c character varying(255),
              rating_date__c timestamp with time zone,
              CONSTRAINT pk_jvs_founder_ratings_id PRIMARY KEY (id)
            );
    `);
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_jvs_founder_ratings_account__c ON jvs_founder_ratings (account__c);
    `);
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_jvs_founder_ratings_rating_date__c ON jvs_founder_ratings (rating_date__c);
    `);
    await queryRunner.query(`
      ALTER TABLE jvs_founder_ratings ADD CONSTRAINT fk_jvs_founder_ratings_account__c FOREIGN KEY (account__c) REFERENCES jvs_accounts(id) ON DELETE SET NULL ON UPDATE NO ACTION;
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE jvs_founder_ratings DROP CONSTRAINT IF EXISTS fk_jvs_founder_ratings_account__c;
    `);
    await queryRunner.query(`
      DROP INDEX IF EXISTS idx_jvs_founder_ratings_rating_date__c;
    `);
    await queryRunner.query(`
      DROP INDEX IF EXISTS idx_jvs_founder_ratings_account__c;
    `);
    await queryRunner.query(`
      DROP TABLE IF EXISTS jvs_founder_ratings;
    `);
  }
}
