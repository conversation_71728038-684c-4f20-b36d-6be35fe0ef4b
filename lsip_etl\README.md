# LSIP ETL Service

NestJS-based ETL service that pulls data from Salesforce via REST APIs, processes it, and persists the results in PostgreSQL.

## Requirements

- Node.js 18+ (logical assignment operators are required by the tooling)
- npm 9+
- Access to the target Salesforce org and PostgreSQL instance

## Getting Started

```bash
npm install
```

Create a `.env` file at the project root (or update the existing one) with the connection details for both back ends.

### Environment Variables

| Variable | Description |
| --- | --- |
| `DB_HOST` | PostgreSQL host name |
| `DB_PORT` | PostgreSQL port number |
| `DB_USER` | PostgreSQL database user |
| `DB_PASS` | PostgreSQL user password |
| `DB_NAME` | PostgreSQL database name |
| `DB_SSL_MODE` | Optional SSL mode (`disable`, `require`, `verify-full`; defaults to `require`) |
| `SF_BASE_URL` | Salesforce instance base URL for data requests |
| `SF_AUTH_URL` | Optional OAuth token URL (defaults to `SF_BASE_URL`) |
| `SF_CLIENT_ID` | Connected app client ID |
| `SF_CLIENT_SECRET` | Connected app client secret |
| `SF_USERNAME` | Salesforce integration user |
| `SF_PASSWORD` | Salesforce integration password |
| `SF_TOKEN` | Salesforce security token (appended to the password) |
| `SF_API_VERSION` | Optional REST API version (defaults to `v57.0`) |
| `SF_TOKEN_TTL_MS` | Optional access token cache lifetime in milliseconds (defaults to 30 minutes) |

## Running the Service

```bash
# development
npm run start

# watch mode
npm run start:dev

# production build
npm run build
npm run start:prod
```

## Testing

```bash
npm run test
npm run test:e2e
npm run test:cov
```

## Project Structure Highlights

- `src/config` – configuration modules, env validation, Salesforce/PostgreSQL settings
- `src/database` – TypeORM database bootstrap with auto-loaded entities
- `src/salesforce` – Salesforce authentication + REST client utilities

These modules are registered globally through `AppModule`, making the Salesforce and PostgreSQL services available across the application.
