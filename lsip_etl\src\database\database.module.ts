/* eslint-disable */
import { Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';

@Module({
  imports: [
    TypeOrmModule.forRootAsync({
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        const sslMode = configService.get<string>('database.sslMode');
        const sslOption = (() => {
          if (!sslMode || sslMode === 'disable') {
            return false;
          }
          if (sslMode === 'verify-full') {
            return { rejectUnauthorized: true };
          }
          return { rejectUnauthorized: false };
        })();

        return {
          type: 'postgres',
          host: configService.get<string>('database.host'),
          port: configService.get<number>('database.port'),
          username: configService.get<string>('database.username'),
          password: configService.get<string>('database.password'),
          database: configService.get<string>('database.name'),
          ssl: sslOption,
          autoLoadEntities: true,
          synchronize: false,
          migrations: ['dist/migrations/*.js', 'src/migrations/*.ts'],
          migrationsTableName: 'migrations',
        };
      },
    }),
  ],
  exports: [TypeOrmModule],
})
export class DatabaseModule {}
