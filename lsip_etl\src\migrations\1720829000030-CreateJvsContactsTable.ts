/* eslint-disable */
import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateJvsContactsTable1720829000030 implements MigrationInterface {
  name = 'CreateJvsContactsTable1720829000030';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS jvs_contacts (
              id character varying(18) NOT NULL,
              isdeleted boolean,
              masterrecordid character varying(18),
              accountid character varying(18),
              lastname character varying(80),
              firstname character varying(40),
              salutation character varying(40),
              middlename character varying(40),
              name character varying(121),
              phone character varying(40),
              fax character varying(40),
              mobilephone character varying(40),
              assistantphone character varying(40),
              reportstoid character varying(18),
              email character varying(80),
              title character varying(128),
              department character varying(80),
              assistantname character varying(40),
              birthdate date,
              hasoptedoutofemail boolean,
              hasoptedoutoffax boolean,
              donotcall boolean,
              titletype character varying(40),
              departmentgroup character varying(40),
              company_fund__c character varying(18),
              dayofmonth__c double precision,
              designation_rank__c double precision,
              dob__c date,
              director__c boolean,
              department_2__c character varying(255),
              designation__c character varying(100),
              key_contact__c boolean,
              rating__c character varying(255),
              CONSTRAINT pk_jvs_contacts_id PRIMARY KEY (id)
            );
    `);
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_jvs_contacts_accountid ON jvs_contacts (accountid);
    `);
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_jvs_contacts_company_fund__c ON jvs_contacts (company_fund__c);
    `);
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_jvs_contacts_reportstoid ON jvs_contacts (reportstoid);
    `);
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_jvs_contacts_email ON jvs_contacts (email);
    `);
    await queryRunner.query(`
      ALTER TABLE jvs_contacts ADD CONSTRAINT fk_jvs_contacts_accountid FOREIGN KEY (accountid) REFERENCES jvs_accounts(id) ON DELETE SET NULL ON UPDATE NO ACTION;
    `);
    await queryRunner.query(`
      ALTER TABLE jvs_contacts ADD CONSTRAINT fk_jvs_contacts_company_fund__c FOREIGN KEY (company_fund__c) REFERENCES jvs_company_fund(id) ON DELETE SET NULL ON UPDATE NO ACTION;
    `);
    await queryRunner.query(`
      ALTER TABLE jvs_contacts ADD CONSTRAINT fk_jvs_contacts_reportstoid FOREIGN KEY (reportstoid) REFERENCES jvs_contacts(id) ON DELETE SET NULL ON UPDATE NO ACTION;
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE jvs_contacts DROP CONSTRAINT IF EXISTS fk_jvs_contacts_reportstoid;
    `);
    await queryRunner.query(`
      ALTER TABLE jvs_contacts DROP CONSTRAINT IF EXISTS fk_jvs_contacts_company_fund__c;
    `);
    await queryRunner.query(`
      ALTER TABLE jvs_contacts DROP CONSTRAINT IF EXISTS fk_jvs_contacts_accountid;
    `);
    await queryRunner.query(`
      DROP INDEX IF EXISTS idx_jvs_contacts_email;
    `);
    await queryRunner.query(`
      DROP INDEX IF EXISTS idx_jvs_contacts_reportstoid;
    `);
    await queryRunner.query(`
      DROP INDEX IF EXISTS idx_jvs_contacts_company_fund__c;
    `);
    await queryRunner.query(`
      DROP INDEX IF EXISTS idx_jvs_contacts_accountid;
    `);
    await queryRunner.query(`
      DROP TABLE IF EXISTS jvs_contacts;
    `);
  }
}
