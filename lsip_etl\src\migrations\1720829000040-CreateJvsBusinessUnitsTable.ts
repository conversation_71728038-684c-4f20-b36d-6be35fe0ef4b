/* eslint-disable */
import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateJvsBusinessUnitsTable1720829000040 implements MigrationInterface {
  name = 'CreateJvsBusinessUnitsTable1720829000040';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS jvs_business_units (
              id character varying(18) NOT NULL,
              name character varying(80),
              company__c character varying(18),
              parent_business__c character varying(18),
              active__c boolean,
              benchmark__c character varying(18),
              type__c character varying(255),
              CONSTRAINT pk_jvs_business_units_id PRIMARY KEY (id)
            );
    `);
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_jvs_business_units_company__c ON jvs_business_units (company__c);
    `);
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_jvs_business_units_parent_business__c ON jvs_business_units (parent_business__c);
    `);
    await queryRunner.query(`
      ALTER TABLE jvs_business_units ADD CONSTRAINT fk_jvs_business_units_company__c FOREIGN KEY (company__c) REFERENCES jvs_accounts(id) ON DELETE SET NULL ON UPDATE NO ACTION;
    `);
    await queryRunner.query(`
      ALTER TABLE jvs_business_units ADD CONSTRAINT fk_jvs_business_units_parent_business__c FOREIGN KEY (parent_business__c) REFERENCES jvs_business_units(id) ON DELETE SET NULL ON UPDATE NO ACTION;
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE jvs_business_units DROP CONSTRAINT IF EXISTS fk_jvs_business_units_parent_business__c;
    `);
    await queryRunner.query(`
      ALTER TABLE jvs_business_units DROP CONSTRAINT IF EXISTS fk_jvs_business_units_company__c;
    `);
    await queryRunner.query(`
      DROP INDEX IF EXISTS idx_jvs_business_units_parent_business__c;
    `);
    await queryRunner.query(`
      DROP INDEX IF EXISTS idx_jvs_business_units_company__c;
    `);
    await queryRunner.query(`
      DROP TABLE IF EXISTS jvs_business_units;
    `);
  }
}
