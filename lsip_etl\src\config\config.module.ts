/* eslint-disable */
import { Module } from '@nestjs/common';
import { ConfigModule as NestConfigModule } from '@nestjs/config';
import databaseConfig from './database.config';
import salesforceConfig from './salesforce.config';
import { validate } from './env.validation';

@Module({
  imports: [
    NestConfigModule.forRoot({
      isGlobal: true,
      load: [databaseConfig, salesforceConfig],
      validate,
    }),
  ],
  exports: [NestConfigModule],
})
export class ConfigModule {}
