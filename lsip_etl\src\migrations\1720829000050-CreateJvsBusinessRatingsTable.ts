/* eslint-disable */
import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateJvsBusinessRatingsTable1720829000050 implements MigrationInterface {
  name = 'CreateJvsBusinessRatingsTable1720829000050';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS jvs_business_ratings (
              id character varying(18) NOT NULL,
              name character varying(80),
              createddate timestamp with time zone,
              account__c character varying(18),
              product_vision_strategy__c character varying(255),
              product_vision_strategy_des__c character varying(4000),
              gtm_muscle_ent_sales_partnership_des__c character varying(4000),
              gtm_muscle_ent_sales_partnership__c character varying(255),
              data_technology_orientation__c character varying(255),
              data_technology_orientation_des__c character varying(4000),
              deep_tech_engg_product_leadership__c character varying(255),
              deep_tech_engg_product_leadership_des__c character varying(4000),
              gtm_muscle_commercialization__c character varying(255),
              gtm_muscle_commercialization_des__c character varying(4000),
              regulatory__c character varying(255),
              regulatory_des__c character varying(4000),
              product_vision_strategy_ai_ml__c character varying(255),
              product_vision_strategy_ai_ml_des__c character varying(4000),
              gtm_muscle_for_devtools_prosumer__c character varying(255),
              gtm_muscle_for_devtools_prosumer_des__c character varying(4000),
              narrative_building__c character varying(255),
              narrative_building_desc__c character varying(4000),
              product_vision_strategy_ai_llm__c character varying(255),
              product_vision_strategy_ai_llm_desc__c character varying(4000),
              gtm_muscle__c character varying(255),
              gtm_muscle_desc__c character varying(4000),
              positioning_narrative_building__c character varying(255),
              positioning_narrative_building_desc__c character varying(4000),
              product_strategy_strategic_thinking__c character varying(255),
              product_strategy_strategic_thinking_des__c character varying(4000),
              marketing_narrative_building__c character varying(255),
              marketing_narrative_building_desc__c character varying(4000),
              ai_capability__c character varying(255),
              ai_capability_desc__c character varying(4000),
              category_creation__c character varying(255),
              category_creation_desc__c character varying(4000),
              CONSTRAINT pk_jvs_business_ratings_id PRIMARY KEY (id)
            );
    `);
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_jvs_business_ratings_account__c ON jvs_business_ratings (account__c);
    `);
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_jvs_business_ratings_createddate ON jvs_business_ratings (createddate);
    `);
    await queryRunner.query(`
      ALTER TABLE jvs_business_ratings ADD CONSTRAINT fk_jvs_business_ratings_account__c FOREIGN KEY (account__c) REFERENCES jvs_accounts(id) ON DELETE SET NULL ON UPDATE NO ACTION;
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE jvs_business_ratings DROP CONSTRAINT IF EXISTS fk_jvs_business_ratings_account__c;
    `);
    await queryRunner.query(`
      DROP INDEX IF EXISTS idx_jvs_business_ratings_createddate;
    `);
    await queryRunner.query(`
      DROP INDEX IF EXISTS idx_jvs_business_ratings_account__c;
    `);
    await queryRunner.query(`
      DROP TABLE IF EXISTS jvs_business_ratings;
    `);
  }
}
