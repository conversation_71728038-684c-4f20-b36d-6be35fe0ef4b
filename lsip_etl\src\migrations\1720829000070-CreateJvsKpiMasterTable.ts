/* eslint-disable */
import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateJvsKpiMasterTable1720829000070 implements MigrationInterface {
  name = 'CreateJvsKpiMasterTable1720829000070';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS jvs_kpi_master (
              id character varying(18) NOT NULL,
              name character varying(80),
              currencyisocode character varying(3),
              category__c character varying(255),
              type__c character varying(255),
              unit__c character varying(255),
              CONSTRAINT pk_jvs_kpi_master_id PRIMARY KEY (id)
            );
    `);
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_jvs_kpi_master_category__c ON jvs_kpi_master (category__c);
    `);
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_jvs_kpi_master_type__c ON jvs_kpi_master (type__c);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      DROP INDEX IF EXISTS idx_jvs_kpi_master_type__c;
    `);
    await queryRunner.query(`
      DROP INDEX IF EXISTS idx_jvs_kpi_master_category__c;
    `);
    await queryRunner.query(`
      DROP TABLE IF EXISTS jvs_kpi_master;
    `);
  }
}
