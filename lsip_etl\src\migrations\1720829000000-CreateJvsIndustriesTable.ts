/* eslint-disable */
import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateJvsIndustriesTable1720829000000 implements MigrationInterface {
  name = 'CreateJvsIndustriesTable1720829000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS jvs_industries (
              id character varying(18) NOT NULL,
              name character varying(80),
              parent_industry__c character varying(18),
              frequency__c character varying(255),
              CONSTRAINT pk_jvs_industries_id PRIMARY KEY (id)
            );
    `);
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_jvs_industries_parent_industry__c ON jvs_industries (parent_industry__c);
    `);
    await queryRunner.query(`
      ALTER TABLE jvs_industries ADD CONSTRAINT fk_jvs_industries_parent_industry__c FOREIGN KEY (parent_industry__c) REFERENCES jvs_industries(id) ON DELETE SET NULL ON UPDATE NO ACTION;
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE jvs_industries DROP CONSTRAINT IF EXISTS fk_jvs_industries_parent_industry__c;
    `);
    await queryRunner.query(`
      DROP INDEX IF EXISTS idx_jvs_industries_parent_industry__c;
    `);
    await queryRunner.query(`
      DROP TABLE IF EXISTS jvs_industries;
    `);
  }
}
