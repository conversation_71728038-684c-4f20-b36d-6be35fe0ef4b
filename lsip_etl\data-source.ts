/* eslint-disable */
import 'dotenv/config';
import { DataSource } from 'typeorm';

const sslMode = process.env.DB_SSL_MODE ?? 'require';

const sslOption = (() => {
  if (!sslMode || sslMode === 'disable') {
    return false;
  }
  if (sslMode === 'verify-full') {
    return { rejectUnauthorized: true };
  }
  return { rejectUnauthorized: false };
})();

const AppDataSource = new DataSource({
  type: 'postgres',
  host: process.env.DB_HOST ?? 'localhost',
  port: Number(process.env.DB_PORT ?? 5432),
  username: process.env.DB_USER ?? 'postgres',
  password: process.env.DB_PASS ?? '',
  database: process.env.DB_NAME ?? 'postgres',
  ssl: sslOption,
  entities: [],
  migrations: ['dist/migrations/*.js', 'src/migrations/*.ts'],
  migrationsTableName: 'migrations',
});

export default AppDataSource;
