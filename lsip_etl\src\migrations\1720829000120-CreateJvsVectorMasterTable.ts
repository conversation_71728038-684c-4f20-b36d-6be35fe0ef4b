/* eslint-disable */
import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateJvsVectorMasterTable1720829000120 implements MigrationInterface {
  name = 'CreateJvsVectorMasterTable1720829000120';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS jvs_vector_master (
              id character varying(18) NOT NULL,
              name character varying(80),
              currencyisocode character varying(3),
              CONSTRAINT pk_jvs_vector_master_id PRIMARY KEY (id)
            );
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      DROP TABLE IF EXISTS jvs_vector_master;
    `);
  }
}
