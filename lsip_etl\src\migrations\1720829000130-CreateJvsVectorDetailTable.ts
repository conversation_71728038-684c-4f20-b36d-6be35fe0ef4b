/* eslint-disable */
import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateJvsVectorDetailTable1720829000130 implements MigrationInterface {
  name = 'CreateJvsVectorDetailTable1720829000130';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS jvs_vector_detail (
              id character varying(18) NOT NULL,
              name character varying(80),
              currencyisocode character varying(3),
              kpi_particular__c character varying(18),
              name__c character varying(255),
              unique_identifier__c character varying(255),
              vector_master__c character varying(18),
              CONSTRAINT pk_jvs_vector_detail_id PRIMARY KEY (id)
            );
    `);
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_jvs_vector_detail_kpi_particular__c ON jvs_vector_detail (kpi_particular__c);
    `);
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_jvs_vector_detail_vector_master__c ON jvs_vector_detail (vector_master__c);
    `);
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_jvs_vector_detail_unique_identifier__c ON jvs_vector_detail (unique_identifier__c);
    `);
    await queryRunner.query(`
      ALTER TABLE jvs_vector_detail ADD CONSTRAINT fk_jvs_vector_detail_kpi_particular__c FOREIGN KEY (kpi_particular__c) REFERENCES jvs_kpi_perticular(id) ON DELETE SET NULL ON UPDATE NO ACTION;
    `);
    await queryRunner.query(`
      ALTER TABLE jvs_vector_detail ADD CONSTRAINT fk_jvs_vector_detail_vector_master__c FOREIGN KEY (vector_master__c) REFERENCES jvs_vector_master(id) ON DELETE SET NULL ON UPDATE NO ACTION;
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE jvs_vector_detail DROP CONSTRAINT IF EXISTS fk_jvs_vector_detail_vector_master__c;
    `);
    await queryRunner.query(`
      ALTER TABLE jvs_vector_detail DROP CONSTRAINT IF EXISTS fk_jvs_vector_detail_kpi_particular__c;
    `);
    await queryRunner.query(`
      DROP INDEX IF EXISTS idx_jvs_vector_detail_unique_identifier__c;
    `);
    await queryRunner.query(`
      DROP INDEX IF EXISTS idx_jvs_vector_detail_vector_master__c;
    `);
    await queryRunner.query(`
      DROP INDEX IF EXISTS idx_jvs_vector_detail_kpi_particular__c;
    `);
    await queryRunner.query(`
      DROP TABLE IF EXISTS jvs_vector_detail;
    `);
  }
}
