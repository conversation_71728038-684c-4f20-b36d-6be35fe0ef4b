/* eslint-disable */
const REQUIRED_ENV_VARS = [
  'DB_HOST',
  'DB_PORT',
  'DB_USER',
  'DB_PASS',
  'DB_NAME',
  'SF_BASE_URL',
  'SF_CLIENT_ID',
  'SF_CLIENT_SECRET',
  'SF_USERNAME',
  'SF_PASSWORD',
  'SF_TOKEN',
];

export function validate(config: Record<string, unknown>): Record<string, unknown> {
  const missing = REQUIRED_ENV_VARS.filter((key) => {
    const value = config[key];
    return value === undefined || value === null || value === '';
  });

  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }

  const dbPort = Number(config.DB_PORT);
  if (Number.isNaN(dbPort) || dbPort <= 0) {
    throw new Error('DB_PORT must be a positive number');
  }

  return config;
}
